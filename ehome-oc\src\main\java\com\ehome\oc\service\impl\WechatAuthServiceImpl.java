package com.ehome.oc.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.utils.CacheUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.oc.utils.WxCryptUtils;
import com.ehome.oc.domain.WxUser;
import com.ehome.oc.service.IWechatAuthService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;


import java.util.HashMap;
import java.util.Map;

/**
 * 微信认证服务实现类
 *
 * 职责说明：
 * 1. 负责与微信API的交互，处理微信认证相关的核心功能
 * 2. 提供access_token的获取和缓存管理
 * 3. 处理微信用户数据的解密（手机号、用户信息等）
 * 4. 管理用户认证状态和手机号更新
 *
 * 技术特点：
 * - 使用缓存机制避免频繁调用微信API
 * - 统一的错误处理和日志记录
 * - 支持微信小程序的数据解密
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class WechatAuthServiceImpl implements IWechatAuthService {

    /** 系统日志记录器 */
    private static final Logger logger = LoggerFactory.getLogger("sys-ds");

    /** 微信相关数据缓存键前缀 */
    private static final String WECHAT_CACHE = "wechat-cache";

    /** access_token缓存键 */
    private static final String ACCESS_TOKEN_KEY = "access_token";

    /** 微信小程序AppID，从配置文件读取 */
    @Value("${wechat.appid}")
    private String appId;

    /** 微信小程序AppSecret，从配置文件读取 */
    @Value("${wechat.secret}")
    private String secret;

    /** HTTP请求模板，用于调用微信API */
    @Autowired
    private RestTemplate restTemplate;

    /** 微信获取access_token接口地址 */
    private static final String ACCESS_TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={appid}&secret={secret}";

    /** 微信获取用户手机号接口地址 */
    private static final String PHONE_NUMBER_URL = "https://api.weixin.qq.com/wxa/business/getuserphonenumber";



    private Record wechatUserAdd(WxUser wechatUser){
        String openid = wechatUser.getOpenId();
        Record record = Db.findFirst("select * from eh_wx_user where openid = ?", openid);
        if(record == null){
            Record record1 = new Record().set("openid", openid);
            record1.set("nickname", wechatUser.getNickName());
            record1.set("avatar_url", wechatUser.getAvatarUrl());
            Db.save("eh_wx_user", "user_id", record1);
        } else {
            Record record2 = new Record().set("openid", openid);
            record2.set("user_id", record.getLong("id"));
            record2.set("nickname", wechatUser.getNickName());
            record2.set("avatar_url", wechatUser.getAvatarUrl());
            Db.update("eh_wx_user", "user_id", record2);
        }
        return Db.findFirst("select * from eh_wx_user where openid = ?", openid);
    }

    /**
     * 解密微信用户手机号码
     *
     * 业务流程：
     * 1. 获取微信接口调用凭证access_token
     * 2. 构建HTTP请求头，设置Content-Type为application/json
     * 3. 构建请求体，包含手机号授权码phoneCode
     * 4. 调用微信getuserphonenumber接口获取加密的手机号信息
     * 5. 解析微信返回结果，检查错误码和错误信息
     * 6. 提取并返回解密后的手机号码、纯手机号等信息
     *
     * 注意事项：
     * - phoneCode只能使用一次，重复使用会失败
     * - 需要用户在小程序端主动授权获取手机号
     * - access_token必须是有效的，否则接口调用失败
     *
     * @param code 微信登录code（用于日志记录和错误追踪）
     * @param phoneCode 手机号授权码，用户授权后获得的一次性code
     * @return AjaxResult 包含解密后手机号的结果，成功时data包含phoneNumber等信息
     */
    @Override
    public AjaxResult decryptPhoneNumber(String code, String phoneCode) {
        try {
            // 步骤1：获取微信接口调用凭证access_token
            String accessToken = getAccessToken();
            if (StringUtils.isEmpty(accessToken)) {
                logger.error("解密手机号失败：获取access_token失败，code: {}", code);
                return AjaxResult.error("获取access_token失败");
            }

            // 步骤2：构建HTTP请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 步骤3：构建请求体，包含手机号授权码
            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("code", phoneCode);

            // 步骤4：发送请求到微信接口获取手机号
            HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(requestBody, headers);
            String url = PHONE_NUMBER_URL + "?access_token=" + accessToken;
            ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);

            // 5. 解析响应
            JSONObject jsonResponse = JSON.parseObject(response.getBody());
            if (jsonResponse.getInteger("errcode") == 0) {
                JSONObject phoneInfo = jsonResponse.getJSONObject("phone_info");
                String phoneNumber = phoneInfo.getString("phoneNumber");

                Map<String, Object> result = new HashMap<>();
                result.put("phoneNumber", phoneNumber);
                return AjaxResult.success(result);
            } else {
                return AjaxResult.error("获取手机号失败: " + jsonResponse.getString("errmsg"));
            }
        } catch (Exception e) {
            return AjaxResult.error("获取手机号失败: " + e.getMessage());
        }
    }

    /**
     * 获取微信接口调用凭证access_token
     *
     * 业务流程：
     * 1. 首先从缓存中查找是否存在有效的access_token
     * 2. 如果缓存中存在且未过期，直接返回缓存的access_token
     * 3. 如果缓存中没有或已过期，调用微信API获取新的access_token
     * 4. 解析微信返回结果，提取access_token
     * 5. 将新获取的access_token存入缓存
     * 6. 返回access_token供其他接口使用
     *
     * 缓存策略：
     * - 微信access_token默认有效期为7200秒（2小时）
     * - 使用系统缓存工具CacheUtils进行缓存管理
     *
     * 错误处理：
     * - 网络异常时返回null，由调用方处理
     * - 微信接口返回错误时返回null
     *
     * @return String access_token 微信接口调用凭证，失败时返回null
     */
    private String getAccessToken() {
        try {
            // 步骤1：先从缓存获取access_token
            String accessToken = (String) CacheUtils.get(WECHAT_CACHE, ACCESS_TOKEN_KEY);
            if (StringUtils.isNotEmpty(accessToken)) {
                logger.debug("从缓存获取access_token成功");
                return accessToken;
            }

            // 步骤2：缓存中没有，调用微信接口获取新的access_token
            logger.info("缓存中无access_token，调用微信接口获取");
            Map<String, String> params = new HashMap<>();
            params.put("appid", appId);
            params.put("secret", secret);

            // 步骤3：发送HTTP请求到微信服务器
            ResponseEntity<String> response = restTemplate.getForEntity(
                ACCESS_TOKEN_URL,
                String.class,
                params
            );

            // 步骤4：解析微信返回结果
            JSONObject jsonResponse = JSON.parseObject(response.getBody());
            if (jsonResponse.containsKey("access_token")) {
                accessToken = jsonResponse.getString("access_token");

                // 步骤5：存入缓存
                CacheUtils.put(WECHAT_CACHE, ACCESS_TOKEN_KEY, accessToken);
                logger.info("获取access_token成功并已缓存");
                return accessToken;
            } else {
                logger.error("获取access_token失败: {}", jsonResponse.getString("errmsg"));
            }
            return null;
        } catch (Exception e) {
            logger.error("获取access_token异常: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 更新用户手机号码
     *
     * 业务流程：
     * 1. 根据用户ID更新eh_wx_user表中的mobile字段
     * 2. 执行数据库更新操作
     * 3. 返回更新结果状态
     *
     * 使用场景：
     * - 用户首次授权手机号后更新到数据库
     * - 用户更换手机号时的信息同步
     *
     * @param userId 微信用户ID，对应eh_wx_user表的user_id字段
     * @param phoneNumber 新的手机号码，11位数字格式
     * @return AjaxResult 更新结果，成功返回success，失败返回error及错误信息
     */
    @Override
    public AjaxResult updateUserPhone(String userId, String phoneNumber) {
        try {
            logger.info("更新用户手机号：userId={}, phoneNumber={}", userId, phoneNumber);

            // 步骤1：执行数据库更新操作
            int updateCount = Db.update("update eh_wx_user set mobile = ? where user_id = ?", phoneNumber, userId);

            if (updateCount > 0) {
                logger.info("用户手机号更新成功：userId={}", userId);
                return AjaxResult.success("更新手机号成功");
            } else {
                logger.warn("用户手机号更新失败，未找到对应用户：userId={}", userId);
                return AjaxResult.error("未找到对应用户");
            }
        } catch (Exception e) {
            logger.error("更新用户手机号异常：userId={}, error={}", userId, e.getMessage(), e);
            return AjaxResult.error("更新手机号失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户认证状态
     *
     * 业务说明：
     * 当前实现返回固定的认证状态，主要用于兼容旧版本接口
     * 实际的认证状态判断逻辑在UserStatusService中实现
     *
     * @return AjaxResult 包含认证状态的结果，isHouseAuth表示房屋认证状态
     */
    @Override
    public AjaxResult getAuthStatus() {
        Map<String, Object> authStatus = new HashMap<>();
        authStatus.put("isHouseAuth", true);
        logger.debug("获取认证状态：{}", authStatus);
        return AjaxResult.success(authStatus);
    }

    /**
     * 解密微信用户信息
     *
     * 业务流程：
     * 1. 验证解密所需的三个参数是否完整
     * 2. 使用微信提供的解密算法解密用户数据
     * 3. 将解密后的JSON字符串转换为JSONObject
     * 4. 返回解密后的用户信息
     *
     * 解密参数说明：
     * - sessionKey: 微信会话密钥，通过wx.login获取
     * - encryptedData: 加密的用户数据
     * - iv: 初始向量，用于解密算法
     *
     * 返回的用户信息通常包含：
     * - openId: 用户唯一标识
     * - nickName: 用户昵称
     * - avatarUrl: 用户头像URL
     * - gender: 用户性别
     * - country, province, city: 用户地理位置信息
     *
     * @param sessionKey 微信会话密钥
     * @param encryptedData 加密的用户数据
     * @param iv 初始向量
     * @return AjaxResult 解密结果，成功时data包含用户信息JSONObject
     */
    @Override
    public AjaxResult decryptUserInfo(String sessionKey, String encryptedData, String iv) {
        try {
            // 步骤1：验证解密参数完整性
            if (StringUtils.isEmpty(sessionKey) || StringUtils.isEmpty(encryptedData) || StringUtils.isEmpty(iv)) {
                logger.warn("解密用户信息失败：参数不完整");
                return AjaxResult.error("解密参数不能为空");
            }

            // 步骤2：使用微信解密工具解密数据
            String decryptedData = WxCryptUtils.decrypt(sessionKey, encryptedData, iv);

            // 步骤3：将解密后的JSON字符串转换为对象
            JSONObject userInfo = JSON.parseObject(decryptedData);

            logger.info("解密用户信息成功，openId: {}", userInfo.getString("openId"));
            return AjaxResult.success("解密成功", userInfo);
        } catch (Exception e) {
            logger.error("解密用户信息失败: " + e.getMessage(), e);
            return AjaxResult.error("解密用户信息失败: " + e.getMessage());
        }
    }

    private String[] getOpenId(String code) {
        String url = "https://api.weixin.qq.com/sns/jscode2session?appid=" + appId + "&secret=" + secret + "&js_code=" + code + "&grant_type=authorization_code";
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> entity = new HttpEntity<>(headers);

        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
        String responseBody = response.getBody();

        if (response.getStatusCode() == HttpStatus.OK && responseBody != null) {
            JSONObject jsonObject = JSON.parseObject(responseBody);
            String openid = jsonObject.getString("openid");
            String sessionKey = jsonObject.getString("session_key");
            String unionId = jsonObject.getString("unionid"); // 尝试获取unionid
            return new String[]{openid, sessionKey, unionId};
        } else {
            return null;
        }
    }

}