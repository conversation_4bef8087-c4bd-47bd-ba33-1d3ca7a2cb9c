package com.ehome.oc.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ehome.common.exception.ServiceException;
import com.ehome.common.utils.IpUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.http.HttpUtils;
import com.ehome.oc.domain.WxUser;
import com.ehome.oc.domain.dto.WxLoginDTO;
import com.ehome.oc.mapper.WxUserMapper;
import com.ehome.oc.service.IWxUserService;
import com.ehome.oc.utils.WxCryptUtils;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 微信用户服务实现类
 *
 * 职责说明：
 * 1. 负责微信用户的业务逻辑处理，包括用户登录、身份验证、信息查询等核心业务功能
 * 2. 处理微信用户的注册、登录、信息更新等生命周期管理
 * 3. 管理用户身份识别（业主身份、物业身份）和角色缓存
 * 4. 提供用户信息查询和状态检查服务
 *
 * 核心功能：
 * - 微信登录处理：支持code登录和已有用户信息登录
 * - 用户身份识别：自动识别用户的业主或物业身份
 * - 用户信息管理：创建、更新、查询用户基本信息
 * - 登录状态维护：记录登录时间、IP等信息
 *
 * 技术特点：
 * - 支持事务处理，确保数据一致性
 * - 集成微信API，处理用户数据解密
 * - 提供角色缓存机制，提升查询性能
 * - 完善的异常处理和日志记录
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class WxUserServiceImpl implements IWxUserService {
    /** 日志记录器 */
    private static final Logger log = LoggerFactory.getLogger(WxUserServiceImpl.class);

    /** 微信小程序AppID，从配置文件读取 */
    @Value("${wechat.appid}")
    private String appid;

    /** 微信小程序AppSecret，从配置文件读取 */
    @Value("${wechat.secret}")
    private String secret;

    /** 微信用户数据访问层 */
    @Autowired
    private WxUserMapper wxUserMapper;

    /** HTTP请求对象，用于获取客户端信息 */
    @Autowired
    private HttpServletRequest request;

    /**
     * 微信用户登录处理
     *
     * 业务流程：
     * 1. 提取登录参数（openid、unionid、sessionKey等）
     * 2. 如果提供了加密数据，尝试解密获取完整用户信息（昵称、头像等）
     * 3. 获取客户端真实IP地址
     * 4. 根据openid查询数据库中是否存在该用户
     * 5. 如果用户不存在，创建新用户记录并标记为首次登录
     * 6. 如果用户存在，更新用户信息（昵称、头像、会话密钥等）
     * 7. 更新用户登录时间和IP地址
     * 8. 返回完整的用户信息对象
     *
     * 数据处理逻辑：
     * - 优先使用解密后的用户信息（昵称、头像）
     * - 如果解密失败，使用默认值或保持原有信息
     * - 支持手机号的直接设置和更新
     * - 自动生成用户ID（新用户）
     *
     * 事务处理：
     * - 使用@Transactional确保数据一致性
     * - 新用户创建和老用户更新在同一事务中
     *
     * @param loginDTO 登录数据传输对象，包含openid、unionid、sessionKey、加密数据等
     * @return WxUser 微信用户对象，包含完整的用户信息
     * @throws ServiceException 当登录处理失败时抛出业务异常
     */
    @Override
    @Transactional
    public WxUser wxLogin(WxLoginDTO loginDTO) {
        try {
            // 步骤1：提取登录参数
            String code = loginDTO.getCode();
            String openid = loginDTO.getOpenid();
            String unionid = loginDTO.getUnionid();
            String sessionKey = loginDTO.getSessionKey();

            // 步骤2：如果提供了加密数据，尝试解密获取完整用户信息
            JSONObject decryptedUserInfo = null;
            if (StringUtils.isNotEmpty(loginDTO.getEncryptedData()) &&
                StringUtils.isNotEmpty(loginDTO.getIv()) &&
                StringUtils.isNotEmpty(sessionKey)) {
                try {
                    String decryptedData = WxCryptUtils.decrypt(sessionKey, loginDTO.getEncryptedData(), loginDTO.getIv());
                    decryptedUserInfo = JSON.parseObject(decryptedData);
                    log.info("成功解密用户信息: openid={}", openid);
                } catch (Exception e) {
                    log.warn("解密用户信息失败，将使用默认信息: {}", e.getMessage());
                }
            }

            // 获取真实IP
            String ip = IpUtils.getIpAddr(request);

            // 查询用户是否存在
            WxUser wxUser = selectWxUserByOpenid(openid);
            boolean isFirstLogin = false;

            if (wxUser == null) {
                // 新用户，自动注册
                isFirstLogin = true;
                wxUser = new WxUser();
                wxUser.setOpenId(openid);
                wxUser.setUnionId(unionid);
                wxUser.setSessionKey(sessionKey);
                // 如果提供了手机号，直接设置
                if (StringUtils.isNotEmpty(loginDTO.getPhoneNumber())) {
                    wxUser.setMobile(loginDTO.getPhoneNumber());
                }
                // 使用解密后的用户信息或默认值
                if (decryptedUserInfo != null) {
                    wxUser.setNickName(decryptedUserInfo.getString("nickName"));
                    wxUser.setAvatarUrl(decryptedUserInfo.getString("avatarUrl"));
                    wxUser.setGender(String.valueOf(decryptedUserInfo.getIntValue("gender")));
                } else {
                    wxUser.setNickName("微信用户");
                }
                wxUser.setStatus("0");
                wxUser.setLoginIp(ip);
                wxUser.setLoginDate(new Date());
                wxUserMapper.insertWxUser(wxUser);
            } else {
                if (unionid != null && !unionid.equals(wxUser.getUnionId())) {
                    wxUser.setUnionId(unionid);
                }
                if (sessionKey != null && !sessionKey.equals(wxUser.getSessionKey())) {
                    wxUser.setSessionKey(sessionKey);
                }
                wxUser.setLoginIp(ip);
                wxUser.setLoginDate(new Date());
                wxUserMapper.updateWxUser(wxUser);
            }

            // 设置首次登录标识
            wxUser.setIsFirstLogin(isFirstLogin);

            Record wxLoginLog = new Record();
            wxLoginLog.set("user_id", wxUser.getUserId());
            wxLoginLog.set("mobile", wxUser.getMobile());
            wxLoginLog.set("login_ip", ip);
            wxLoginLog.set("login_date", new Date());
            wxLoginLog.set("user_agent", request.getHeader("User-Agent"));
            Db.save("wx_login_log","log_id", wxLoginLog);

            return wxUser;
        } catch (Exception e) {
            log.error("微信登录失败", e);
            throw new ServiceException("微信登录失败: " + e.getMessage());
        }
    }

    @Override
    public WxUser selectWxUserById(Long userId) {
        return wxUserMapper.selectWxUserById(userId);
    }

    @Override
    public int updateWxUser(WxUser user) {
        return wxUserMapper.updateWxUser(user);
    }

    @Override
    public WxUser selectWxUserByOpenid(String openid) {
        return wxUserMapper.selectWxUserByOpenid(openid);
    }

    /**
     * 根据code获取微信用户信息（openid、unionid、session_key）
     */
    private JSONObject getWxUserInfoByCode(String code) {
        if("0".equals(code)){
            JSONObject wxUserInfo = new JSONObject();
            wxUserInfo.put("openid", "0");
            wxUserInfo.put("unionid", "0");
            wxUserInfo.put("session_key", "0");
            return wxUserInfo;
        }

        try {
            // 微信登录凭证校验接口
            String url = String.format("https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code",
                    appid, secret, code);

            String response = HttpUtils.sendGet(url);
            JSONObject jsonObject = JSON.parseObject(response);

            // 判断是否成功
            if (jsonObject.containsKey("errcode") && jsonObject.getIntValue("errcode") != 0) {
                log.error("获取微信用户信息失败: {}", response);
                return null;
            }

            return jsonObject;
        } catch (Exception e) {
            log.error("调用微信接口失败", e);
            return null;
        }
    }

    @Override
    public void updateUserLoginInfo(Long userId, String loginIp) {
        try {
            WxUser wxUser = new WxUser();
            wxUser.setUserId(userId);
            wxUser.setLoginIp(loginIp);
            wxUser.setLoginDate(new Date());
            wxUserMapper.updateWxUser(wxUser);
            log.info("更新用户登录信息成功，用户ID: {}, 登录IP: {}", userId, loginIp);
        } catch (Exception e) {
            log.error("更新用户登录信息失败，用户ID: {}, 错误: {}", userId, e.getMessage(), e);
        }
    }

    /**
     * 通过微信code检查用户是否存在
     *
     * 业务流程：
     * 1. 调用微信接口获取用户基本信息（openid、unionid、session_key等）
     * 2. 验证微信返回的openid是否有效
     * 3. 根据openid查询数据库中的用户记录
     * 4. 记录查询结果日志
     * 5. 返回用户对象（存在）或null（不存在）
     *
     * 使用场景：
     * - 用户登录前的状态检查
     * - 判断是新用户注册还是老用户登录
     * - 智能登录流程的第一步
     *
     * 注意事项：
     * - code只能使用一次，重复使用会失败
     * - 网络异常或微信接口异常时返回null
     * - 不会修改用户数据，仅做查询操作
     *
     * @param code 微信登录授权码，用户授权后获得的一次性code
     * @return WxUser 用户对象（如果存在）或null（如果不存在或查询失败）
     */
    @Override
    public WxUser checkUserByCode(String code) {
        try {
            // 步骤1：调用微信接口获取用户基本信息
            JSONObject wxUserInfo = getWxUserInfoByCode(code);
            if (wxUserInfo == null) {
                log.warn("获取微信用户信息失败，code: {}", code);
                return null;
            }

            // 步骤2：验证返回的openid是否有效
            String openid = wxUserInfo.getString("openid");
            if (StringUtils.isEmpty(openid)) {
                log.warn("微信返回的openid为空，code: {}", code);
                return null;
            }

            // 步骤3：根据openid查询数据库中的用户记录
            WxUser wxUser = selectWxUserByOpenid(openid);
            log.info("检查用户是否存在: openid={}, 用户存在={}", openid, wxUser != null);

            return wxUser;
        } catch (Exception e) {
            log.error("检查用户失败，code: {}, 错误: {}", code, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public Map<String, Object> checkUserWithWxInfo(String code) {
        try {
            // 调用微信接口获取用户信息
            JSONObject wxUserInfo = getWxUserInfoByCode(code);
            if (wxUserInfo == null) {
                log.warn("获取微信用户信息失败，code: {}", code);
                throw new RuntimeException("获取微信用户信息失败");
            }

            String openid = wxUserInfo.getString("openid");
            String unionid = wxUserInfo.getString("unionid");
            String sessionKey = wxUserInfo.getString("session_key");

            if (StringUtils.isEmpty(openid)) {
                log.warn("微信返回的openid为空，code: {}", code);
                throw new RuntimeException("微信返回的openid为空");
            }

            // 查询用户是否存在
            WxUser existingUser = selectWxUserByOpenid(openid);
            boolean userExists = existingUser != null;

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("userExists", userExists);
            result.put("openid", openid);
            result.put("unionid", unionid);
            result.put("sessionKey", sessionKey);

            if (userExists) {
                result.put("userId", existingUser.getUserId());
                result.put("nickName", existingUser.getNickName());
                result.put("avatarUrl", existingUser.getAvatarUrl());
                result.put("mobile", existingUser.getMobile());
            }

            log.info("检查用户完成: openid={}, 用户存在={}", openid, userExists);
            return result;

        } catch (Exception e) {
            log.error("检查用户失败，code: {}, 错误: {}", code, e.getMessage(), e);
            throw new RuntimeException("检查用户失败: " + e.getMessage(), e);
        }
    }

    @Override
    public WxUser selectWxUserByMobile(String mobile) {
        if (StringUtils.isEmpty(mobile)) {
            return null;
        }
        try {
            Record record = Db.findFirst("select * from eh_wx_user where mobile = ? and status = '0'", mobile);
            if (record != null) {
                WxUser wxUser = new WxUser();
                wxUser.setUserId(record.getLong("user_id"));
                wxUser.setOpenId(record.getStr("openid"));
                wxUser.setUnionId(record.getStr("unionid"));
                wxUser.setNickName(record.getStr("nick_name"));
                wxUser.setMobile(record.getStr("mobile"));
                wxUser.setCommunityId(record.getStr("community_id"));
                wxUser.setOwnerId(record.getStr("owner_id"));
                wxUser.setUserRoles(record.getStr("user_roles"));
                return wxUser;
            }
            return null;
        } catch (Exception e) {
            log.error("根据手机号查询微信用户失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public Map<String, Object> checkUserIdentityByPhone(String phoneNumber) {
        return checkUserIdentityByPhoneWithCache(phoneNumber, null);
    }

    /**
     * 通过手机号检查用户身份（业主或物业），支持角色缓存
     *
     * 业务流程：
     * 1. 如果没有传入wxUser对象，先通过手机号查询微信用户
     * 2. 检查用户是否已有角色缓存，如有则直接使用缓存数据
     * 3. 如果没有缓存，查询业主信息表检查是否为业主身份
     * 4. 查询系统用户表检查是否为物业身份
     * 5. 更新用户角色缓存到数据库，提升后续查询性能
     * 6. 构建身份信息结果，包含详细的业主/物业信息
     * 7. 返回完整的身份信息数据
     *
     * 缓存策略：
     * - 将用户角色信息缓存到eh_wx_user表的user_roles字段
     * - 支持多角色：owner（业主）、property（物业）
     * - 缓存命中时直接返回，避免重复数据库查询
     *
     * 返回数据结构：
     * - hasOwnerIdentity: 是否具有业主身份
     * - hasPropertyIdentity: 是否具有物业身份
     * - ownerUser: 业主详细信息（如果是业主）
     * - propertyUser: 物业详细信息（如果是物业）
     *
     * @param phoneNumber 用户手机号，11位数字格式
     * @param wxUser 微信用户对象，可为null（会自动查询）
     * @return Map<String, Object> 身份信息结果，包含角色标识和详细信息
     */
    @Override
    public Map<String, Object> checkUserIdentityByPhoneWithCache(String phoneNumber, WxUser wxUser) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 步骤1：如果没有传入wxUser，先通过手机号查询
            if (wxUser == null) {
                wxUser = selectWxUserByMobile(phoneNumber);
            }

            // 步骤2：检查是否已有角色缓存
            if (wxUser != null && StringUtils.isNotEmpty(wxUser.getUserRoles())) {
                boolean hasOwnerIdentity = wxUser.hasRole("owner");
                boolean hasPropertyIdentity = wxUser.hasRole("property");

                result.put("hasOwnerIdentity", hasOwnerIdentity);
                result.put("hasPropertyIdentity", hasPropertyIdentity);

                // 查询详细信息
                result.put("ownerUser", hasOwnerIdentity ? getOwnerInfo(wxUser, phoneNumber) : null);
                result.put("propertyUser", hasPropertyIdentity ? getPropertyInfo(phoneNumber) : null);

                log.info("使用缓存的身份信息: 手机号={}, 角色={}", phoneNumber, wxUser.getUserRoles());
                return result;
            }

            Record ownerRecord = getOwnerRecord(wxUser,phoneNumber);
            
            Record propertyRecord = Db.findFirst(
                "select user_id, login_name, user_name, phonenumber, community_id, dept_id, status " +
                "from sys_user where phonenumber = ? and status = '0' and del_flag = '0'",
                phoneNumber
            );

            boolean hasOwnerIdentity = ownerRecord != null;
            boolean hasPropertyIdentity = propertyRecord != null;

            // 更新角色缓存
            if (wxUser != null && (hasOwnerIdentity || hasPropertyIdentity)) {
                String roles = "";
                if (hasOwnerIdentity) roles = "owner";
                if (hasPropertyIdentity) roles = roles.isEmpty() ? "property" : roles + ",property";

                wxUser.setUserRoles(roles);
                updateWxUser(wxUser);
                log.info("更新用户角色缓存: 手机号={}, 角色={}", phoneNumber, roles);
            }

            result.put("hasOwnerIdentity", hasOwnerIdentity);
            result.put("hasPropertyIdentity", hasPropertyIdentity);
            result.put("ownerUser", hasOwnerIdentity ? buildOwnerInfo(ownerRecord) : null);
            result.put("propertyUser", hasPropertyIdentity ? buildPropertyInfo(propertyRecord) : null);

            log.info("身份识别完成: 手机号={}, 业主身份={}, 物业身份={}", phoneNumber, hasOwnerIdentity, hasPropertyIdentity);

        } catch (Exception e) {
            log.error("身份识别失败: 手机号={}, 错误={}", phoneNumber, e.getMessage(), e);
            result.put("hasOwnerIdentity", false);
            result.put("hasPropertyIdentity", false);
            result.put("ownerUser", null);
            result.put("propertyUser", null);
        }

        return result;
    }

    private Record getOwnerRecord(WxUser wxUser,String phoneNumber) {
        Record ownerRecord = null;
        if(wxUser!=null&&StringUtils.isNotEmpty(wxUser.getOwnerId())){
            ownerRecord = Db.findFirst("select owner_id, owner_name, mobile, community_id from eh_owner where owner_id = ?", wxUser.getOwnerId());
        }
        if(ownerRecord==null){
            ownerRecord = Db.findFirst("select owner_id, owner_name, mobile, community_id from eh_owner where mobile = ?", phoneNumber);
        }
        return ownerRecord;
    }

    private Map<String, Object> getOwnerInfo(WxUser wxUser,String phoneNumber) {
        Record ownerRecord =  getOwnerRecord(wxUser,phoneNumber);
        return ownerRecord != null ? buildOwnerInfo(ownerRecord) : null;
    }

    private Map<String, Object> getPropertyInfo(String phoneNumber) {
        Record propertyRecord = Db.findFirst(
            "select user_id, login_name, user_name, phonenumber, community_id, dept_id, status " +
            "from sys_user where phonenumber = ? and status = '0' and del_flag = '0'",
            phoneNumber
        );
        return propertyRecord != null ? buildPropertyInfo(propertyRecord) : null;
    }

    private Map<String, Object> buildOwnerInfo(Record ownerRecord) {
        Map<String, Object> ownerUser = new HashMap<>();
        ownerUser.put("ownerId", ownerRecord.getStr("owner_id"));
        ownerUser.put("ownerName", ownerRecord.getStr("owner_name"));
        ownerUser.put("mobile", ownerRecord.getStr("mobile"));
        ownerUser.put("communityId", ownerRecord.getStr("community_id"));
        return ownerUser;
    }

    private Map<String, Object> buildPropertyInfo(Record propertyRecord) {
        Map<String, Object> propertyUser = new HashMap<>();
        propertyUser.put("userId", propertyRecord.getLong("user_id"));
        propertyUser.put("loginName", propertyRecord.getStr("login_name"));
        propertyUser.put("userName", propertyRecord.getStr("user_name"));
        propertyUser.put("phonenumber", propertyRecord.getStr("phonenumber"));
        propertyUser.put("communityId", propertyRecord.getStr("community_id"));
        propertyUser.put("deptId", propertyRecord.getLong("dept_id"));
        return propertyUser;
    }

}