package com.ehome.oc.service.impl;

import com.ehome.common.core.domain.model.LoginUser;
import com.ehome.common.utils.StringUtils;
import com.ehome.oc.domain.HouseInfo;
import com.ehome.oc.domain.OwnerInfo;
import com.ehome.oc.service.IHouseInfoService;
import com.ehome.oc.service.UserStatusService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 用户状态服务实现类
 * 统一处理用户状态相关的业务逻辑
 */
@Service
public class UserStatusServiceImpl implements UserStatusService {
    
    private static final Logger logger = LoggerFactory.getLogger(UserStatusServiceImpl.class);
    
    @Autowired
    private IHouseInfoService houseInfoService;
    
    // 简单的内存缓存，生产环境建议使用Redis
    private final Map<String, CacheItem> cache = new HashMap<>();
    
    // 缓存项
    private static class CacheItem {
        Object data;
        long timestamp;
        long expireTime;
        
        CacheItem(Object data, long expireTime) {
            this.data = data;
            this.timestamp = System.currentTimeMillis();
            this.expireTime = expireTime;
        }
        
        boolean isExpired() {
            return System.currentTimeMillis() - timestamp > expireTime;
        }
    }
    
    /**
     * 构建用户状态数据（默认包含订阅状态）
     *
     * @param currentUser 当前登录用户
     * @return Map<String, Object> 用户状态数据
     */
    @Override
    public Map<String, Object> buildUserStatusData(LoginUser currentUser) {
        return buildUserStatusData(currentUser, true);
    }

    /**
     * 构建用户状态数据
     * 统一处理用户状态相关的业务逻辑，避免代码重复，提升系统性能
     *
     * 业务流程：
     * 1. 检查用户类型，物业用户返回简化状态信息
     * 2. 获取或查询业主ID，如果没有则通过手机号查询
     * 3. 获取房屋信息（带缓存），包括业主信息和房屋列表
     * 4. 查找默认房屋，确定房屋认证状态
     * 5. 获取社区信息（带缓存），包含社区配置和服务信息
     * 6. 获取订阅状态（带缓存，可选），检查消息订阅情况
     * 7. 构建完整的状态数据对象
     * 8. 异常处理，确保返回基本数据避免完全失败
     *
     * 缓存策略：
     * - 房屋信息缓存30分钟，减少数据库查询
     * - 社区信息缓存60分钟，社区配置变更较少
     * - 订阅状态缓存5分钟，需要及时反映订阅变化
     *
     * 数据结构：
     * - ownerInfo: 业主基本信息
     * - communityInfo: 社区信息和配置
     * - isHouseAuth: 房屋认证状态
     * - houseInfo: 默认房屋信息
     * - tokenUser: 登录用户信息
     * - userType: 用户类型（1-业主，2-物业）
     * - isPropertyUser: 是否为物业用户
     * - subscribeStatus: 订阅状态（可选）
     *
     * @param currentUser 当前登录用户对象
     * @param includeSubscribeStatus 是否包含订阅状态，登录时可设为false提升性能
     * @return Map<String, Object> 用户状态数据，包含认证状态、房屋信息、社区信息等
     */
    @Override
    public Map<String, Object> buildUserStatusData(LoginUser currentUser, boolean includeSubscribeStatus) {
        Map<String, Object> data = new HashMap<>();

        try {
            logger.info("构建用户状态数据: userId={}, userType={}, includeSubscribe={}",
                       currentUser.getUserId(), currentUser.getUserType(), includeSubscribeStatus);

            // 步骤1：检查用户类型，物业用户特殊处理
            if ("2".equals(currentUser.getUserType())) {
                // 物业用户，返回简化的状态信息
                data.put("isPropertyUser", true);
                data.put("userType", "2");
                data.put("tokenUser", currentUser);
                data.put("isHouseAuth", true); // 物业用户默认认证通过
                logger.info("物业用户状态数据构建完成");
                return data;
            }
            
            String ownerId = currentUser.getOwnerId();
            if (StringUtils.isEmpty(ownerId)) {
                // 尝试通过手机号查询业主信息
                String mobile = currentUser.getMobile();
                if (StringUtils.isNotEmpty(mobile)) {
                    logger.info("业主ID为空，尝试通过手机号查询业主信息: {}", mobile);
                    Record ownerRecord = Db.findFirst("select * from eh_owner where mobile = ?", mobile);
                    if (ownerRecord != null) {
                        ownerId = ownerRecord.getStr("owner_id");
                        currentUser.setOwnerId(ownerId);
                        logger.info("通过手机号查询到业主ID: {}", ownerId);
                    } else {
                        logger.info("手机号 {} 未找到对应的业主信息", mobile);
                    }
                }

                // 如果仍然没有ownerId，返回未认证状态
                if (StringUtils.isEmpty(ownerId)) {
                    logger.info("无法获取业主ID，返回未认证状态");
                    data.put("isHouseAuth", false);
                    data.put("tokenUser", currentUser);
                    data.put("userType", "1");
                    data.put("isPropertyUser", false);
                    return data;
                }
            }
            
            // 获取房屋信息（带缓存）
            Map<String, Object> houseData = getHouseInfoWithCache(ownerId);
            OwnerInfo ownerInfo = (OwnerInfo) houseData.get("ownerInfo");
            @SuppressWarnings("unchecked")
            List<HouseInfo> houseList = (List<HouseInfo>) houseData.get("houseList");
            
            // 查找默认房屋
            HouseInfo defaultHouse = null;
            boolean isHouseAuth = false;
            
            if (houseList != null && !houseList.isEmpty()) {
                isHouseAuth = true;
                for (HouseInfo house : houseList) {
                    if (house.getIsDefault() != null && house.getIsDefault() == 1) {
                        defaultHouse = house;
                        break;
                    }
                }
                if (defaultHouse == null) {
                    defaultHouse = houseList.get(0);
                }
                if (ownerInfo != null) {
                    ownerInfo.setHouseStr(defaultHouse.getCombinaName() + "/" + defaultHouse.getRoom());
                }
            }
            
            // 获取社区信息（带缓存）
            Map<String, Object> communityData = null;
            if (ownerInfo != null && StringUtils.isNotEmpty(ownerInfo.getCommunityId())) {
                communityData = getCommunityInfoWithCache(ownerInfo.getCommunityId());
            }
            
            // 获取订阅状态（带缓存）
            Map<String, Object> subscribeStatus = null;
            if (includeSubscribeStatus) {
                subscribeStatus = getSubscribeStatusWithCache(String.valueOf(currentUser.getUserId()));
            }
            
            // 构建返回数据
            data.put("ownerInfo", ownerInfo);
            data.put("communityInfo", communityData);
            data.put("isHouseAuth", isHouseAuth);
            data.put("houseInfo", defaultHouse);
            data.put("tokenUser", currentUser);
            data.put("userType", "1");
            data.put("isPropertyUser", false);
            
            if (subscribeStatus != null) {
                data.put("subscribeStatus", subscribeStatus);
            }
            
            logger.info("用户状态数据构建完成: isHouseAuth={}, hasCommunityInfo={}", 
                       isHouseAuth, communityData != null);
            
        } catch (Exception e) {
            logger.error("构建用户状态数据失败: " + e.getMessage(), e);
            // 返回基本数据，避免完全失败
            data.put("isHouseAuth", false);
            data.put("tokenUser", currentUser);
            data.put("userType", currentUser.getUserType() != null ? currentUser.getUserType() : "1");
            data.put("isPropertyUser", "2".equals(currentUser.getUserType()));
        }
        
        return data;
    }
    
    @Override
    public Map<String, Object> getHouseInfoWithCache(String ownerId) {
        String cacheKey = "houseInfo_" + ownerId;
        CacheItem cached = cache.get(cacheKey);
        
        if (cached != null && !cached.isExpired()) {
            logger.info("使用缓存的房屋信息: {}", ownerId);
            @SuppressWarnings("unchecked")
            Map<String, Object> result = (Map<String, Object>) cached.data;
            return result;
        }
        
        // 查询数据库
        logger.info("查询房屋信息: {}", ownerId);
        OwnerInfo ownerInfo = new OwnerInfo();
        List<HouseInfo> houseList = new ArrayList<>();
        
        List<Record> houseRecordList = Db.find(
            "SELECT t1.*,t2.house_info,t2.owner_id,t2.owner_name,t2.role,t2.mobile,t3.is_default user_default " +
            "FROM eh_house_info t1,eh_owner t2,eh_house_owner_rel t3 " +
            "WHERE t1.house_id = t3.house_id AND t2.owner_id = t3.owner_id AND t2.owner_id = ?", 
            ownerId);
        
        if (houseRecordList != null && !houseRecordList.isEmpty()) {
            // 只设置一次ownerInfo，使用第一条记录的信息
            Record firstRecord = houseRecordList.get(0);
            ownerInfo.setOwnerId(firstRecord.getStr("owner_id"));
            ownerInfo.setOwnerName(firstRecord.getStr("owner_name"));
            ownerInfo.setCommunityId(firstRecord.getStr("community_id"));
            ownerInfo.setCommunityName(firstRecord.getStr("community_name"));
            ownerInfo.setMobile(firstRecord.getStr("mobile"));
            ownerInfo.setRole(firstRecord.getStr("role"));
            
            // 处理所有房屋信息
            for (Record record : houseRecordList) {
                HouseInfo house = houseInfoService.recordToObj(record);
                houseList.add(house);
            }
        }
        
        // 缓存结果（30分钟）
        Map<String, Object> result = new HashMap<>();
        result.put("ownerInfo", ownerInfo);
        result.put("houseList", houseList);
        
        cache.put(cacheKey, new CacheItem(result, 30 * 60 * 1000)); // 30分钟
        logger.info("房屋信息已缓存: {}", ownerId);
        
        return result;
    }
    
    @Override
    public Map<String, Object> getCommunityInfoWithCache(String communityId) {
        String cacheKey = "communityInfo_" + communityId;
        CacheItem cached = cache.get(cacheKey);
        
        if (cached != null && !cached.isExpired()) {
            logger.info("使用缓存的社区信息: {}", communityId);
            @SuppressWarnings("unchecked")
            Map<String, Object> result = (Map<String, Object>) cached.data;
            return result;
        }
        
        // 查询数据库
        logger.info("查询社区信息: {}", communityId);
        Record communityRecord = Db.findFirst("SELECT * FROM eh_community WHERE oc_id = ?", communityId);
        
        if (communityRecord != null) {
            Map<String, Object> communityInfo = new HashMap<>();
            communityInfo.put("address", communityRecord.get("oc_address"));
            communityInfo.put("servicePhone", communityRecord.get("service_phone"));
            communityInfo.put("extJson", communityRecord.get("ext_json"));
            communityInfo.put("communityId", communityId);
            communityInfo.put("communityName", communityRecord.getStr("oc_name"));
            communityInfo.put("communityBanner", "/static/images/banner.png");
            communityInfo.put("updateTime", communityRecord.get("update_time"));
            communityInfo.put("cacheTime", System.currentTimeMillis());
            
            // 缓存结果（60分钟）
            cache.put(cacheKey, new CacheItem(communityInfo, 60 * 60 * 1000)); // 60分钟
            logger.info("社区信息已缓存: {}", communityId);
            
            return communityInfo;
        }
        
        return null;
    }
    
    @Override
    public Map<String, Object> getSubscribeStatusWithCache(String userId) {
        String cacheKey = "subscribeStatus_" + userId;
        CacheItem cached = cache.get(cacheKey);
        
        if (cached != null && !cached.isExpired()) {
            logger.info("使用缓存的订阅状态: {}", userId);
            @SuppressWarnings("unchecked")
            Map<String, Object> result = (Map<String, Object>) cached.data;
            return result;
        }
        
        // 查询订阅状态
        Map<String, Object> subscribeStatus = new HashMap<>();
        
        try {
            // 模板ID常量
            String TEMPLATE_PROPERTY_NOTICE = "oFsuyzgpcAvENaOqokhmD-Fa8EmBpDsh_QFLccPmRUY";
            
            // 查询用户是否已订阅物业通知
            Record subscribeRecord = Db.findFirst(
                "SELECT * FROM eh_wx_subscribe_record WHERE wx_user_id = ? AND template_id = ? AND status = 1",
                userId, TEMPLATE_PROPERTY_NOTICE
            );
            
            boolean needSubscribe = true;
            String reason = "未订阅物业通知";
            
            if (subscribeRecord != null) {
                // 检查是否过期（一次性订阅）
                Date expireTime = subscribeRecord.getDate("expire_time");
                if (expireTime == null || expireTime.after(new Date())) {
                    needSubscribe = false;
                    reason = "已订阅";
                } else {
                    // 已过期，更新状态
                    Db.update("UPDATE eh_wx_subscribe_record SET status = 0 WHERE id = ?",
                             subscribeRecord.getStr("id"));
                    reason = "订阅已过期";
                }
            }
            
            subscribeStatus.put("needSubscribe", needSubscribe);
            subscribeStatus.put("reason", reason);
            subscribeStatus.put("templateType", "property_notice");
            subscribeStatus.put("templateId", TEMPLATE_PROPERTY_NOTICE);
            
            // 缓存结果（5分钟）
            cache.put(cacheKey, new CacheItem(subscribeStatus, 5 * 60 * 1000)); // 5分钟
            logger.info("订阅状态已缓存: {}", userId);
            
        } catch (Exception e) {
            logger.error("检查订阅状态失败: {}", e.getMessage(), e);
            subscribeStatus.put("needSubscribe", false);
            subscribeStatus.put("reason", "检查失败");
        }
        
        return subscribeStatus;
    }
    
    /**
     * 清除用户相关缓存
     *
     * 使用场景：
     * 1. 用户信息发生变更时，清除相关缓存确保数据一致性
     * 2. 房屋信息变更时，清除房屋缓存
     * 3. 社区配置更新时，清除社区缓存
     * 4. 订阅状态变化时，清除订阅缓存
     * 5. 系统维护时，批量清理过期缓存
     *
     * 清理策略：
     * - 支持选择性清理，传入null的参数不会被清理
     * - 清理操作是安全的，不存在的缓存键不会报错
     * - 每次清理都会记录日志，便于问题追踪
     *
     * 注意事项：
     * - 清理缓存后下次查询会重新从数据库获取
     * - 频繁清理会影响系统性能，应谨慎使用
     * - 建议在数据变更的事务提交后再清理缓存
     *
     * @param ownerId 业主ID，清除房屋信息缓存
     * @param communityId 社区ID，清除社区信息缓存
     * @param userId 用户ID，清除订阅状态缓存
     */
    @Override
    public void clearUserCache(String ownerId, String communityId, String userId) {
        // 清除房屋信息缓存
        if (StringUtils.isNotEmpty(ownerId)) {
            cache.remove("houseInfo_" + ownerId);
            logger.info("已清除房屋缓存: {}", ownerId);
        }

        // 清除社区信息缓存
        if (StringUtils.isNotEmpty(communityId)) {
            cache.remove("communityInfo_" + communityId);
            logger.info("已清除社区缓存: {}", communityId);
        }

        // 清除订阅状态缓存
        if (StringUtils.isNotEmpty(userId)) {
            cache.remove("subscribeStatus_" + userId);
            logger.info("已清除订阅状态缓存: {}", userId);
        }
    }
}
