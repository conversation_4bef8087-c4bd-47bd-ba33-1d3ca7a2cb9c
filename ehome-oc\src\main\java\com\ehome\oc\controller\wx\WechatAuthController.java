package com.ehome.oc.controller.wx;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.core.controller.BaseWxController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.domain.model.LoginUser;
import com.ehome.common.utils.IpUtils;
import com.ehome.common.utils.SecurityUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.oc.domain.OwnerInfo;
import com.ehome.oc.domain.WxUser;
import com.ehome.oc.domain.dto.WxLoginDTO;
import com.ehome.oc.service.IHouseInfoService;
import com.ehome.oc.service.IWechatAuthService;
import com.ehome.oc.service.IWxUserService;
import com.ehome.oc.service.UserStatusService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 微信认证控制器
 *
 * 职责说明：
 * 1. 微信认证的HTTP接口控制器，负责处理前端的认证请求
 * 2. 协调各种认证流程，包括智能登录、用户检查、身份选择等
 * 3. 统一处理微信用户的登录、注册、认证状态管理
 * 4. 提供token管理功能，包括生成、刷新、验证等
 *
 * 核心接口：
 * - smartLogin: 智能登录接口，自动检测用户状态并处理登录流程
 * - refreshToken: 刷新用户token，延长登录有效期
 * - selectIdentity: 选择用户身份（业主或物业）
 * - checkUser: 检查用户是否存在（已合并到smartLogin）
 *
 * 技术特点：
 * - 继承BaseWxController，提供微信相关的基础功能
 * - 统一的异常处理和错误响应
 * - 完善的参数验证和安全检查
 * - 集成用户状态服务，提供一致的状态数据
 *
 * 接口路径：/api/wx/auth
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/wx/auth")
public class WechatAuthController extends BaseWxController {

    /** 微信用户服务，处理用户相关业务逻辑 */
    @Autowired
    private IWxUserService wxUserService;

    /** 微信认证服务，处理认证相关功能 */
    @Autowired
    private IWechatAuthService wechatAuthService;

    /** HTTP请求对象，用于获取客户端信息 */
    @Autowired
    private HttpServletRequest request;

    /** 房屋信息服务，处理房屋相关业务 */
    @Autowired
    private IHouseInfoService houseInfoService;

    /** 用户状态服务，统一处理用户状态数据 */
    @Autowired
    private UserStatusService userStatusService;


    /**
     * 智能登录接口 - 自动检测用户状态并处理登录流程
     *
     * 功能说明：
     * 合并了checkUser和oneClickLogin的功能，减少前后端交互次数，提升用户体验
     *
     * 业务流程：
     * 1. 参数验证和安全检查，提取必要的登录参数
     * 2. 判断是否需要调用微信接口（支持前端传递checkResult避免重复调用）
     * 3. 如果是首次调用，通过code获取微信用户信息并检查用户是否存在
     * 4. 根据用户状态决定处理方式：
     *    - 老用户：直接执行登录流程，生成token和状态数据
     *    - 新用户无手机号：返回需要手机号授权状态，引导用户授权
     *    - 新用户有手机号：执行注册流程，创建用户并登录
     * 5. 构建完整的登录响应数据，包含用户信息、认证状态、token等
     * 6. 记录登录日志和异常处理
     *
     * 参数说明：
     * - code: 微信登录授权码（首次调用必需）
     * - phoneCode: 手机号授权码（新用户注册时需要）
     * - encryptedData: 加密的用户信息（可选，用于获取昵称头像）
     * - iv: 解密初始向量（与encryptedData配套使用）
     * - openid: 微信用户唯一标识（复用时传递）
     * - unionid: 微信开放平台统一标识（复用时传递）
     * - sessionKey: 微信会话密钥（复用时传递）
     * - userExists: 用户是否存在标识（复用时传递）
     *
     * 返回数据：
     * - userInfo: 用户基本信息
     * - token: 登录凭证（认证通过时）
     * - authStatus: 认证状态（none/pending/verified）
     * - needPhoneAuth: 是否需要手机号授权
     * - needHouseAuth: 是否需要房屋认证
     * - isFirstLogin: 是否首次登录
     * - 其他状态数据（房屋信息、社区信息等）
     *
     * @param params 登录参数Map，包含code、phoneCode、encryptedData等
     * @return AjaxResult 登录结果，包含用户信息、认证状态、token等完整数据
     */
    @PostMapping("/smartLogin")
    public AjaxResult smartLogin(@RequestBody Map<String, Object> params) {
        try {
            // 步骤1：参数验证和安全检查，提取登录参数
            String code = (String) params.get("code");
            String phoneCode = (String) params.get("phoneCode");
            String encryptedData = (String) params.get("encryptedData");
            String iv = (String) params.get("iv");

            // 支持前端传递的checkResult，避免重复调用微信接口
            String openid = (String) params.get("openid");
            String unionid = (String) params.get("unionid");
            String sessionKey = (String) params.get("sessionKey");
            Boolean userExists = (Boolean) params.get("userExists");

            logger.info("智能登录开始 - code:{}, openid:{}, unionid:{}, sessionKey:{}, userExists:{}",
                       code, openid, unionid, sessionKey, userExists);

            // 基本参数验证：code和openid至少要有一个
            if (StringUtils.isEmpty(code) && StringUtils.isEmpty(openid)) {
                logger.warn("智能登录失败: 微信登录code和openid都为空");
                return AjaxResult.error("登录参数不能为空");
            }


            // 安全检查：验证code格式（仅在code不为空时检查）
            if (StringUtils.isNotEmpty(code) && !isValidWxCode(code)) {
                logger.warn("智能登录失败: 微信登录code格式无效");
                return AjaxResult.error("登录参数无效");
            }

            // 安全检查：验证加密数据完整性
            if (StringUtils.isNotEmpty(encryptedData) && StringUtils.isEmpty(iv)) {
                logger.warn("智能登录失败: 加密数据不完整");
                return AjaxResult.error("用户信息参数不完整");
            }

            logger.info("开始智能登录，code长度: {}, 是否复用checkResult: {}",
                StringUtils.isNotEmpty(code) ? code.length() : 0, openid != null);

            Map<String, Object> checkResult;

            // 判断是否需要调用微信接口
            if (StringUtils.isNotEmpty(openid) && userExists != null) {
                // 使用前端传递的checkResult，避免重复调用微信接口
                logger.info("使用前端传递的checkResult，跳过微信接口调用");
                checkResult = new HashMap<>();
                checkResult.put("userExists", userExists);
                checkResult.put("openid", openid);
                checkResult.put("unionid", unionid);
                checkResult.put("sessionKey", sessionKey);
            } else {
                // 第一次调用：调用微信接口获取用户信息并检查用户是否存在
                if (StringUtils.isEmpty(code)) {
                    logger.error("智能登录失败: 没有code且没有有效的checkResult");
                    return AjaxResult.error("登录参数不完整");
                }
                logger.info("第一次调用，获取微信用户信息");
                checkResult = wxUserService.checkUserWithWxInfo(code);
                if (checkResult == null) {
                    logger.error("智能登录失败: 获取微信用户信息失败");
                    return AjaxResult.error("获取微信用户信息失败，请重试");
                }

                // 更新变量值
                userExists = (Boolean) checkResult.get("userExists");
                openid = (String) checkResult.get("openid");
                unionid = (String) checkResult.get("unionid");
                sessionKey = (String) checkResult.get("sessionKey");
            }

            // 验证关键数据
            if (StringUtils.isEmpty(openid)) {
                logger.error("智能登录失败: openid为空");
                return AjaxResult.error("获取用户标识失败，请重试");
            }

            if (userExists == null) {
                logger.error("智能登录失败: 用户存在状态未知");
                return AjaxResult.error("用户状态检查失败，请重试");
            }

            // 第二步：根据用户状态决定处理方式
            if (userExists) {
                // 老用户直接登录
                logger.info("老用户登录，openid长度: {}", openid != null ? openid.length() : 0);
                return handleExistingUserLogin(openid, unionid, sessionKey);
            } else {
                // 新用户注册 - 如果没有手机号授权码，返回特殊状态让前端处理
                if (StringUtils.isEmpty(phoneCode)) {
                    logger.info("新用户需要手机号授权，openid长度: {}", openid != null ? openid.length() : 0);
                    return handleNewUserNeedPhoneAuth(checkResult);
                }
                logger.info("新用户注册，openid长度: {}", openid != null ? openid.length() : 0);
                return handleNewUserRegistration(openid, unionid, sessionKey, phoneCode, encryptedData, iv);
            }

        } catch (Exception e) {
            logger.error("智能登录失败: " + e.getMessage(), e);
            return AjaxResult.error(getErrorMessage(e, "登录失败"));
        }
    }

    /**
     * 处理老用户登录（简化版）
     */
    private AjaxResult handleExistingUserLogin(String openid, String unionid, String sessionKey) {
        return processExistingUserLogin(openid, unionid, sessionKey);
    }

    /**
     * 处理新用户需要手机号授权（简化版）
     */
    private AjaxResult handleNewUserNeedPhoneAuth(Map<String, Object> checkResult) {
        Map<String, Object> data = new HashMap<>();
        data.put("userExists", false);
        data.put("needPhoneAuth", true);
        data.put("message", "新用户需要手机号授权");
        data.put("checkResult", checkResult);
        return AjaxResult.success("需要授权", data);
    }

    /**
     * 处理新用户注册（简化版）
     */
    private AjaxResult handleNewUserRegistration(String openid, String unionid, String sessionKey,
                                               String phoneCode, String encryptedData, String iv) {
        return processNewUserRegistration(openid, unionid, sessionKey, phoneCode, encryptedData, iv);
    }

    /**
     * 身份选择登录接口
     */
    @PostMapping("/selectIdentity")
    public AjaxResult selectIdentity(@RequestBody Map<String, Object> params) {
        try {
            String userType = (String) params.get("userType"); // "1"=业主, "2"=物业
            String openid = (String) params.get("openid");
            String unionid = (String) params.get("unionid");
            String sessionKey = (String) params.get("sessionKey");
            String phoneNumber = (String) params.get("phoneNumber");
            String encryptedData = (String) params.get("encryptedData");
            String iv = (String) params.get("iv");

            // 新增：直接传递用户信息，避免重复查询
            Object ownerUserObj = params.get("ownerUser");
            Object propertyUserObj = params.get("propertyUser");

            if (StringUtils.isEmpty(userType) || StringUtils.isEmpty(openid) || StringUtils.isEmpty(phoneNumber)) {
                return AjaxResult.error("参数不完整");
            }

            logger.info("用户选择身份登录: userType={}, openid长度={}", userType, openid.length());

            if ("1".equals(userType)) {
                // 业主登录
                return processOwnerLoginWithInfo(openid, unionid, sessionKey, phoneNumber, encryptedData, iv, ownerUserObj);
            } else if ("2".equals(userType)) {
                // 物业登录
                return processPropertyLoginWithInfo(openid, phoneNumber, propertyUserObj);
            } else {
                return AjaxResult.error("无效的用户类型");
            }

        } catch (Exception e) {
            logger.error("身份选择登录失败: " + e.getMessage(), e);
            return AjaxResult.error(getErrorMessage(e, "登录失败"));
        }
    }



    @RequestMapping("/check")
    public AjaxResult check() {
        Map<String, Object> data = new HashMap<>();
        JSONObject params =  getParams();
        try {
            LoginUser currentUser = getCurrentUser();
            if(currentUser==null&&params!=null){
                JSONObject tokenUser = params.getJSONObject("tokenUser");
                if(tokenUser!=null){
                    LoginUser loginUser = new LoginUser();
                    loginUser.setOpenId(tokenUser.getString("openId"));
                    loginUser.setUserId(tokenUser.getLong("userId"));
                    loginUser.setUsername(tokenUser.getString("username"));
                    loginUser.setMobile(tokenUser.getString("mobile"));
                    loginUser.setCommunityId(tokenUser.getString("communityId"));
                    loginUser.setOwnerId(tokenUser.getString("ownerId"));
                    loginUser.setHouseId(tokenUser.getString("houseId"));
                    loginUser.setHouseName(tokenUser.getString("houseName"));
                    loginUser.setUserType("1");
                    if(StringUtils.isNotBlank(loginUser.getMobile())&&StringUtils.isNotBlank(loginUser.getCommunityId())){
                        String token = SecurityUtils.createToken(loginUser);
                        data.put("token", token);
                        data.put("tokenUser", loginUser);
                        return AjaxResult.success(data);
                    }
                }
            }
            if (currentUser == null) {
                return AjaxResult.error("用户未登录");
            }else{
                data.put("tokenUser", currentUser);
                return AjaxResult.success(data);
            }
        }catch (Exception e) {
            logger.error("获取认证状态失败: " + e.getMessage(),e);
            return AjaxResult.error("用户未登录");
        }
    }

    private OwnerInfo getOwnerInfo(String ownerId,String mobile) {
        try {
            if (StringUtils.isEmpty(mobile)) {
                logger.warn("获取业主信息失败: 手机号为空");
                return null;
            }

            Record ownerInfo = null;
            if(StringUtils.isEmpty(ownerId)){
                ownerInfo = Db.findFirst("select * from eh_owner where mobile = ?", mobile);
            }else{
                ownerInfo = Db.findFirst("select * from eh_owner where owner_id = ?", ownerId);
            }
            if (ownerInfo != null) {
                OwnerInfo owner = new OwnerInfo();
                owner.setMobile(ownerInfo.getStr("mobile"));
                owner.setOwnerId(ownerInfo.getStr("owner_id"));
                owner.setCommunityId(ownerInfo.getStr("community_id"));
                owner.setOwnerName(ownerInfo.getStr("owner_name"));
                owner.setCommunityId(ownerInfo.getStr("community_id"));
                owner.setHouseCount(ownerInfo.getInt("house_count") != null ? ownerInfo.getInt("house_count") : 0);
                owner.setHouseId(ownerInfo.getStr("house_id"));
                owner.setHouseName(ownerInfo.getStr("house_name"));
                return owner;
            }
        } catch (Exception e) {
            logger.error("获取业主信息失败: " + e.getMessage(), e);
        }
        return null;
    }


    /**
     * 刷新Token接口
     */
    @PostMapping("/refreshToken")
    public AjaxResult refreshToken() {
        try {
            LoginUser currentUser = getCurrentUser();
            if (currentUser == null) {
                return AjaxResult.error("用户未登录");
            }

            // 检查是否启用自动刷新功能
            if (!SecurityUtils.isAutoRefreshEnabled()) {
                return AjaxResult.error("Token刷新功能已禁用");
            }

            // 生成新token
            String newToken = SecurityUtils.createToken(currentUser);

            // 更新用户最后登录时间和IP
            String ip = IpUtils.getIpAddr(request);
            wxUserService.updateUserLoginInfo(currentUser.getUserId(), ip);

            Map<String, Object> data = new HashMap<>();
            data.put("token", newToken);
            data.put("tokenUser", currentUser);
            data.put("expiresIn", SecurityUtils.getExpireTime() / 1000); // 转换为秒
            data.put("refreshThreshold", SecurityUtils.getRefreshThreshold() / 1000); // 转换为秒

            logger.info("Token手动刷新成功，用户ID: {}, 登录IP: {}, 新token有效期: {}ms",
                       currentUser.getUserId(), ip, SecurityUtils.getExpireTime());
            return AjaxResult.success("Token刷新成功", data);

        } catch (Exception e) {
            logger.error("刷新Token失败: " + e.getMessage(), e);
            return AjaxResult.error("刷新Token失败: " + e.getMessage());
        }
    }

    /**
     * 退出登录
     */
    @PostMapping("/logout")
    public AjaxResult logout() {
        try {
            LoginUser loginUser = getCurrentUser();
            if (loginUser != null) {
                // 记录退出日志
                String username = loginUser.getUsername();
                String userId = String.valueOf(loginUser.getUserId());
                logger.info("用户退出登录: " + username + "（" + userId + "）");
                // 清除用户信息
                currentUser.remove();
                return AjaxResult.success("退出成功");
            }
            return AjaxResult.error("用户未登录");
        } catch (Exception e) {
            logger.error("退出登录失败: " + e.getMessage(),e);
            return AjaxResult.error("退出失败: " + e.getMessage());
        }
    }


    /**
     * 更新用户信息
     */
    @PostMapping("/update")
    public AjaxResult updateUserInfo(@RequestBody WxUser wxUser) {
        try {
            Long userId = getCurrentUserId();
            if (userId == null) {
                return AjaxResult.error("用户未登录");
            }

            // 设置用户ID，防止篡改其他用户信息
            wxUser.setUserId(userId);
            
            int rows = wxUserService.updateWxUser(wxUser);
            return rows > 0 ? AjaxResult.success() : AjaxResult.error("更新失败");
        } catch (Exception e) {
            logger.error("更新用户信息失败: " + e.getMessage(),e);
            return AjaxResult.error("更新用户信息失败: " + e.getMessage());
        }
    }



    /**
     * 更绑定手机号接口
     */
    @PostMapping("/updateBindPhone")
    public AjaxResult updateBindPhone(@RequestBody Map<String, String> params) {
        try {
            logger.info("bindPhone params: " + params);
            String phoneCode = params.get("phoneCode");
            String userId = params.get("userId");

            if (StringUtils.isEmpty(phoneCode)) {
                return AjaxResult.error("手机号授权码不能为空");
            }

            if(StringUtils.isEmpty(userId)){
                userId = String.valueOf(getCurrentUserId());
            }

            // 解密手机号
            String phoneNumber = decryptPhoneNumberHelper(phoneCode);
            if (phoneNumber == null) {
                return AjaxResult.error("解密手机号失败");
            }

            // 更新用户手机号
            setCurrentUserMobile(phoneNumber);
            AjaxResult updateResult = wechatAuthService.updateUserPhone(userId, phoneNumber);
            if (!updateResult.isSuccess()) {
                return updateResult;
            }

            // 重新生成token
            LoginUser loginUser = getCurrentUser();
            loginUser.setMobile(phoneNumber);
            String newToken = SecurityUtils.createToken(loginUser);

            Map<String, Object> result = new HashMap<>();
            result.put("token", newToken);
            result.put("tokenUser", loginUser);
            return AjaxResult.success("绑定成功", result);
        } catch (Exception e) {
            logger.error("绑定手机号失败: " + e.getMessage(), e);
            return AjaxResult.error("绑定手机号失败: " + e.getMessage());
        }
    }

    @PostMapping("/bindPhone")
    public AjaxResult bindPhone(@RequestBody Map<String, String> params) {
        try {
            logger.info("bindPhone params: " + params);
            String phoneCode = params.get("phoneCode");
            String userId = params.get("userId");

            if (StringUtils.isEmpty(phoneCode)) {
                return AjaxResult.error("手机号授权码不能为空");
            }

            if(StringUtils.isEmpty(userId)){
                return AjaxResult.error("userId不能为空");
            }
            // 解密手机号
            String phoneNumber = decryptPhoneNumberHelper(phoneCode);
            if (phoneNumber == null) {
                return AjaxResult.error("解密手机号失败");
            }

            AjaxResult updateResult = wechatAuthService.updateUserPhone(userId, phoneNumber);
            if (!updateResult.isSuccess()) {
                return updateResult;
            }
            Map<String, Object> result = new HashMap<>();
            result.put("userId", userId);
            result.put("phoneNumber", phoneNumber);
            return AjaxResult.success("更绑号码成功", result);
        } catch (Exception e) {
            logger.error("绑定手机号失败: " + e.getMessage(), e);
            return AjaxResult.error("绑定手机号失败: " + e.getMessage());
        }
    }

    /**
     * 获取社区信息
     */
    private Map<String, Object> getCommunityInfo(String communityId) {
        try {
            if (StringUtils.isEmpty(communityId)) {
                logger.warn("获取社区信息失败: 社区ID为空");
                return null;
            }

            Record communityRecord = Db.findFirst("select * from eh_community where oc_id = ?", communityId);
            if (communityRecord != null) {
                Map<String, Object> communityInfo = new HashMap<>();
                communityInfo.put("communityId", communityId);
                communityInfo.put("communityName", communityRecord.getStr("oc_name"));
                communityInfo.put("address", communityRecord.getStr("oc_address"));
                communityInfo.put("servicePhone", communityRecord.getStr("service_phone"));
                communityInfo.put("extJson", communityRecord.getStr("ext_json"));
                communityInfo.put("infoJson", communityRecord.getStr("info_json"));
                communityInfo.put("communityBanner", "/static/images/banner.png");
                return communityInfo;
            } else {
                logger.warn("未找到社区信息: communityId = {}", communityId);
            }
        } catch (Exception e) {
            logger.error("获取社区信息失败: " + e.getMessage(), e);
        }
        return null;
    }

    /**
     * 解密手机号的通用方法
     */
    private String decryptPhoneNumberHelper(String phoneCode) {
        try {
            AjaxResult decryptResult = wechatAuthService.decryptPhoneNumber(null, phoneCode);
            if (decryptResult.isSuccess()) {
                @SuppressWarnings("unchecked")
                Map<String, Object> decryptData = (Map<String, Object>) decryptResult.get("data");
                if (decryptData != null) {
                    String phoneNumber = (String) decryptData.get("phoneNumber");
                    if (StringUtils.isNotEmpty(phoneNumber)) {
                        return phoneNumber;
                    }
                }
            }
            logger.error("解密手机号失败: {}", decryptResult.get("msg"));
            return null;
        } catch (Exception e) {
            logger.error("解密手机号异常: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 处理老用户登录
     */
    private AjaxResult processExistingUserLogin(String openid, String unionid, String sessionKey) {
        try {
            // 先获取用户信息，检查手机号
            WxUser wxUser = wxUserService.selectWxUserByOpenid(openid);
            if (wxUser == null) {
                return AjaxResult.error("用户不存在");
            }

            String phoneNumber = wxUser.getMobile();
            if (StringUtils.isNotEmpty(phoneNumber)) {
                // 检查用户身份 - 传入已查询的wxUser避免重复查询
                Map<String, Object> identityResult = wxUserService.checkUserIdentityByPhoneWithCache(phoneNumber, wxUser);
                boolean hasOwner = (Boolean) identityResult.get("hasOwnerIdentity");
                boolean hasProperty = (Boolean) identityResult.get("hasPropertyIdentity");

                // 如果同时具有两种身份，需要用户选择
                if (hasOwner && hasProperty) {
                    Map<String, Object> data = new HashMap<>();
                    data.put("needIdentitySelection", true);
                    data.put("identityResult", identityResult);
                    data.put("openid", openid);
                    data.put("unionid", unionid);
                    data.put("sessionKey", sessionKey);
                    data.put("phoneNumber", phoneNumber);
                    return AjaxResult.success("需要选择登录身份", data);
                }
            }

            // 构建登录DTO（老用户不需要code，直接使用已获取的信息）
            WxLoginDTO loginDTO = new WxLoginDTO();
            loginDTO.setOpenid(openid);
            loginDTO.setUnionid(unionid);
            loginDTO.setSessionKey(sessionKey);

            // 执行登录
            return executeLogin(loginDTO, true);
        } catch (Exception e) {
            logger.error("老用户登录失败: " + e.getMessage(), e);
            return AjaxResult.error(getErrorMessage(e, "登录失败"));
        }
    }

    /**
     * 处理新用户注册
     */
    private AjaxResult processNewUserRegistration(String openid, String unionid, String sessionKey, String phoneCode, String encryptedData, String iv) {
        try {
            // 检查新用户是否提供了手机号授权码
            if (StringUtils.isEmpty(phoneCode)) {
                return AjaxResult.error("新用户注册需要手机号授权");
            }

            // 构建登录DTO（新用户不需要code，直接使用已获取的信息）
            WxLoginDTO loginDTO = new WxLoginDTO();
            loginDTO.setOpenid(openid);
            loginDTO.setUnionid(unionid);
            loginDTO.setSessionKey(sessionKey);
            loginDTO.setEncryptedData(encryptedData);
            loginDTO.setIv(iv);

            // 解密手机号
            String phoneNumber = decryptPhoneNumberHelper(phoneCode);
            if (phoneNumber == null) {
                return AjaxResult.error("解密手机号失败");
            }

            // 检查用户身份
            Map<String, Object> identityResult = wxUserService.checkUserIdentityByPhone(phoneNumber);
            boolean hasOwner = (Boolean) identityResult.get("hasOwnerIdentity");
            boolean hasProperty = (Boolean) identityResult.get("hasPropertyIdentity");

            // 如果同时具有两种身份，需要用户选择
            if (hasOwner && hasProperty) {
                Map<String, Object> data = new HashMap<>();
                data.put("needIdentitySelection", true);
                data.put("identityResult", identityResult);
                data.put("openid", openid);
                data.put("unionid", unionid);
                data.put("sessionKey", sessionKey);
                data.put("phoneNumber", phoneNumber);
                data.put("encryptedData", encryptedData);
                data.put("iv", iv);
                return AjaxResult.success("需要选择登录身份", data);
            }
            loginDTO.setPhoneNumber(phoneNumber);
            logger.info("新用户注册，解密手机号成功");

            // 执行登录
            return executeLogin(loginDTO, false);
        } catch (Exception e) {
            logger.error("新用户注册失败: " + e.getMessage(), e);
            return AjaxResult.error(getErrorMessage(e, "注册失败"));
        }
    }

    /**
     * 执行登录的通用方法
     */
    private AjaxResult executeLogin(WxLoginDTO loginDTO, boolean userExists) {
        try {
            // 执行微信登录
            WxUser wxUser = wxUserService.wxLogin(loginDTO);
            if (wxUser == null) {
                return AjaxResult.error("微信登录失败");
            }
            // 获取首次登录标识
            boolean isFirstLogin = Boolean.TRUE.equals(wxUser.getIsFirstLogin());

            // 创建登录用户信息
            LoginUser loginUser = new LoginUser();
            loginUser.setOpenId(wxUser.getOpenId());
            loginUser.setUserId(wxUser.getUserId());
            loginUser.setNickname(wxUser.getNickName());
            loginUser.setMobile(wxUser.getMobile());
            loginUser.setOwnerId(wxUser.getOwnerId());
            loginUser.setUserType("1");

            // 到这里手机号肯定不为空（新用户已解密，老用户已存在）
            String phoneNumber = wxUser.getMobile();

            // 使用统一的用户状态服务获取用户状态数据
            Map<String, Object> statusData = userStatusService.buildUserStatusData(loginUser, false);

            // 从状态数据中提取信息
            boolean isHouseAuth = (Boolean) statusData.getOrDefault("isHouseAuth", false);
            OwnerInfo ownerInfo = (OwnerInfo) statusData.get("ownerInfo");
            Map<String, Object> communityInfo = (Map<String, Object>) statusData.get("communityInfo");
            String authStatus = isHouseAuth ? "verified" : (ownerInfo != null ? "pending" : "none");

            // 更新登录用户信息
            if (ownerInfo != null) {
                loginUser.setCommunityId(ownerInfo.getCommunityId());
                loginUser.setHouseId(ownerInfo.getHouseId());
                loginUser.setHouseName(ownerInfo.getHouseName());
                loginUser.setUsername(ownerInfo.getOwnerName());
                loginUser.setOwnerId(ownerInfo.getOwnerId());

                // 更新微信用户表中的业主信息
                Db.update("update eh_wx_user set owner_id = ?,community_id = ? where user_id = ?",
                         ownerInfo.getOwnerId(), ownerInfo.getCommunityId(), wxUser.getUserId());
            }

            // 构建返回数据，合并状态数据和登录特有数据
            Map<String, Object> data = new HashMap<>(statusData);
            data.put("userInfo", wxUser);
            data.put("hasBindPhone", true);
            data.put("authStatus", authStatus);
            data.put("needPhoneAuth", false);
            data.put("needHouseAuth", !isHouseAuth); // 只需要检查房屋认证
            data.put("isFirstLogin", isFirstLogin);
            data.put("userExists", userExists); // 添加用户存在标识

            // 只有房屋认证成功才生成token
            if (isHouseAuth) {
                String token = SecurityUtils.createToken(loginUser);
                data.put("token", token);
                logger.info("智能登录成功，用户ID: {}, 用户存在: {}, 房屋认证: {}", wxUser.getUserId(), userExists, isHouseAuth);
            } else {
                data.put("token", "");
                logger.info("房屋认证未完成，不生成token，用户ID: {}", wxUser.getUserId());
            }
            return AjaxResult.success("登录成功", data);
        } catch (Exception e) {
            logger.error("执行登录失败: " + e.getMessage(), e);
            return AjaxResult.error(getErrorMessage(e, "登录失败"));
        }
    }

    /**
     * 验证微信code格式是否有效
     */
    private boolean isValidWxCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return false;
        }
        if("0".equals(code)){
            return true; // 特例：code为0时直接返回true，表示不需要验证
        }

        // 微信code通常是32位字符串，包含字母和数字
        if (code.length() < 20 || code.length() > 50) {
            return false;
        }

        // 检查是否只包含字母、数字、下划线和连字符
        return code.matches("^[a-zA-Z0-9_-]+$");
    }

    

    /**
     * 根据异常类型返回用户友好的错误信息
     */
    private String getErrorMessage(Exception e, String defaultMessage) {
        if (e.getMessage() == null) {
            return defaultMessage;
        }

        String msg = e.getMessage().toLowerCase();

        // 微信相关错误
        if (msg.contains("微信") || msg.contains("wechat")) {
            return "微信服务异常，请稍后重试";
        }

        // 手机号相关错误
        if (msg.contains("手机号") || msg.contains("phone")) {
            return "手机号验证失败，请重试";
        }

        // 网络相关错误
        if (msg.contains("网络") || msg.contains("network") || msg.contains("timeout")) {
            return "网络异常，请检查网络连接";
        }

        // 数据库相关错误
        if (msg.contains("数据库") || msg.contains("database") || msg.contains("sql")) {
            return "系统繁忙，请稍后重试";
        }

        // 其他错误，返回具体错误信息
        return defaultMessage + ": " + e.getMessage();
    }

    /**
     * 处理业主登录
     */
    private AjaxResult processOwnerLogin(String openid, String unionid, String sessionKey,
                                       String phoneNumber, String encryptedData, String iv) {
        return processOwnerLoginWithInfo(openid, unionid, sessionKey, phoneNumber, encryptedData, iv, null);
    }

    /**
     * 处理业主登录（带用户信息）
     */
    private AjaxResult processOwnerLoginWithInfo(String openid, String unionid, String sessionKey,
                                               String phoneNumber, String encryptedData, String iv, Object ownerUserObj) {
        try {
            // 构建登录DTO
            WxLoginDTO loginDTO = new WxLoginDTO();
            loginDTO.setOpenid(openid);
            loginDTO.setUnionid(unionid);
            loginDTO.setSessionKey(sessionKey);
            loginDTO.setPhoneNumber(phoneNumber);
            loginDTO.setEncryptedData(encryptedData);
            loginDTO.setIv(iv);

            // 执行业主登录
            return executeLogin(loginDTO, true);
        } catch (Exception e) {
            logger.error("业主登录失败: " + e.getMessage(), e);
            return AjaxResult.error(getErrorMessage(e, "业主登录失败"));
        }
    }

    /**
     * 处理物业登录
     */
    private AjaxResult processPropertyLogin(String openid, String phoneNumber) {
        return processPropertyLoginWithInfo(openid, phoneNumber, null);
    }

    /**
     * 处理物业登录（带用户信息）
     */
    @SuppressWarnings("unchecked")
    private AjaxResult processPropertyLoginWithInfo(String openid, String phoneNumber, Object propertyUserObj) {
        try {
            Map<String, Object> propertyUserMap;

            if (propertyUserObj != null) {
                // 使用传入的用户信息
                propertyUserMap = (Map<String, Object>) propertyUserObj;
            } else {
                // 查询物业用户信息
                Map<String, Object> identityResult = wxUserService.checkUserIdentityByPhone(phoneNumber);
                propertyUserMap = (Map<String, Object>) identityResult.get("propertyUser");
            }

            if (propertyUserMap == null) {
                return AjaxResult.error("物业账号不存在");
            }

            // 创建物业用户LoginUser
            LoginUser loginUser = new LoginUser();

            // 安全的类型转换
            Object userIdObj = propertyUserMap.get("userId");
            Long userId = userIdObj instanceof Integer ? ((Integer) userIdObj).longValue() : (Long) userIdObj;

            Object deptIdObj = propertyUserMap.get("deptId");
            Long deptId = deptIdObj instanceof Integer ? ((Integer) deptIdObj).longValue() : (Long) deptIdObj;

            loginUser.setUserId(userId);
            loginUser.setOpenId(openid);
            loginUser.setUsername((String) propertyUserMap.get("userName"));
            loginUser.setLoginName((String) propertyUserMap.get("loginName"));
            loginUser.setMobile(phoneNumber);
            loginUser.setUserType("2"); // 物业用户
            loginUser.setCommunityId((String) propertyUserMap.get("communityId"));
            loginUser.setDeptId(deptId);

            // 生成token
            String token = SecurityUtils.createToken(loginUser);

            // 构建返回数据
            Map<String, Object> data = new HashMap<>();
            data.put("token", token);
            data.put("tokenUser", loginUser);
            data.put("userType", "2");
            data.put("isPropertyUser", true);
            data.put("expiresIn", SecurityUtils.getExpireTime() / 1000);
            data.put("refreshThreshold", SecurityUtils.getRefreshThreshold() / 1000);

            logger.info("物业登录成功，用户ID: {}, 用户名: {}", loginUser.getUserId(), loginUser.getUsername());
            return AjaxResult.success("物业登录成功", data);

        } catch (Exception e) {
            logger.error("物业登录失败: " + e.getMessage(), e);
            return AjaxResult.error(getErrorMessage(e, "物业登录失败"));
        }
    }

}